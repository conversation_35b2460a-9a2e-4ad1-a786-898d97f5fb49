describe('Voice Admin Panels', () => {
  beforeEach(() => {
    // Mock authentication and features
    cy.window().then((win) => {
      // Mock useAuth hook
      win.localStorage.setItem('mockAuth', JSON.stringify({
        profile: { role: 'partner', tenant_id: 'test-tenant' },
        isAuthenticated: true,
        isLoading: false,
      }));
      
      // Mock useFeatures hook
      win.localStorage.setItem('mockFeatures', JSON.stringify({
        features: ['voice_intake'],
        isLoading: false,
        hasFeature: () => true,
      }));
    });
  });

  it('should show voice menu for partner with voice_intake feature', () => {
    cy.visit('/admin');
    
    // Check that voice menu items are visible
    cy.contains('Voice Receptionist').should('be.visible');
    cy.contains('Call Logs').should('be.visible');
    cy.contains('Phone Numbers').should('be.visible');
    cy.contains('Failed Notifications').should('be.visible');
  });

  it('should navigate to voice overview page', () => {
    cy.visit('/admin/voice');
    
    // Check that the voice overview page loads
    cy.contains('Voice Receptionist').should('be.visible');
    cy.contains('Manage your voice intake system').should('be.visible');
    
    // Check that action cards are present
    cy.contains('Call Logs').should('be.visible');
    cy.contains('Phone Numbers').should('be.visible');
    cy.contains('Notifications').should('be.visible');
  });

  it('should navigate to call logs page', () => {
    cy.visit('/admin/voice/calls');
    
    // Check that the call logs page loads
    cy.contains('Call Logs').should('be.visible');
    cy.contains('View and manage voice receptionist call history').should('be.visible');
  });

  it('should navigate to phone numbers page', () => {
    cy.visit('/admin/voice/numbers');
    
    // Check that the phone numbers page loads
    cy.contains('Phone Numbers').should('be.visible');
    cy.contains('Search, purchase, and configure Telnyx phone numbers').should('be.visible');
  });

  it('should navigate to notifications page', () => {
    cy.visit('/admin/voice/notifications');
    
    // Check that the notifications page loads
    cy.contains('Failed Notifications').should('be.visible');
    cy.contains('Monitor and manage failed voice system notifications').should('be.visible');
  });

  it('should simulate number purchase flow', () => {
    cy.visit('/admin/voice/numbers');
    
    // Mock API responses
    cy.intercept('GET', '/api/voice/numbers', {
      statusCode: 200,
      body: { numbers: [] }
    }).as('getNumbers');
    
    cy.intercept('POST', '/api/voice/numbers/search', {
      statusCode: 200,
      body: {
        numbers: [
          {
            phone_number: '+1234567890',
            country_code: 'US',
            number_type: 'local',
            monthly_cost: 100,
            features: ['voice', 'sms'],
            locality: 'New York',
            region: 'NY',
          }
        ]
      }
    }).as('searchNumbers');
    
    cy.intercept('POST', '/api/voice/numbers/purchase', {
      statusCode: 200,
      body: { success: true }
    }).as('purchaseNumber');
    
    // Wait for initial load
    cy.wait('@getNumbers');
    
    // Click purchase number button
    cy.contains('Purchase Number').click();
    
    // Search for numbers
    cy.get('input[placeholder="212"]').type('212');
    cy.contains('Search').click();
    
    cy.wait('@searchNumbers');
    
    // Purchase a number
    cy.contains('Purchase').first().click();
    
    // Confirm purchase
    cy.contains('Purchase for $1.00/month').click();
    
    cy.wait('@purchaseNumber');
    
    // Should show success message
    cy.contains('Successfully purchased').should('be.visible');
  });

  it('should redirect non-partner users', () => {
    // Mock non-partner user
    cy.window().then((win) => {
      win.localStorage.setItem('mockAuth', JSON.stringify({
        profile: { role: 'attorney', tenant_id: 'test-tenant' },
        isAuthenticated: true,
        isLoading: false,
      }));
    });
    
    cy.visit('/admin/voice');
    
    // Should redirect to home page
    cy.url().should('eq', Cypress.config().baseUrl + '/');
  });

  it('should redirect users without voice_intake feature', () => {
    // Mock user without voice_intake feature
    cy.window().then((win) => {
      win.localStorage.setItem('mockFeatures', JSON.stringify({
        features: [],
        isLoading: false,
        hasFeature: () => false,
      }));
    });
    
    cy.visit('/admin/voice');
    
    // Should redirect to home page
    cy.url().should('eq', Cypress.config().baseUrl + '/');
  });
});
