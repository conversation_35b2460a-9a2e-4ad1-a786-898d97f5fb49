# Admin Dashboard Guide

The PI Lawyer AI admin dashboard provides comprehensive management capabilities for partner-level users. This guide covers the dashboard structure, features, and access controls.

## Overview

The admin dashboard is accessible at `/admin` and requires partner-level access. It provides centralized management for:

- Tenant management
- User administration  
- Subscription monitoring
- Usage analytics
- Voice receptionist system
- Security monitoring
- System logs and analytics

## Access Control

### Role Requirements
- **Required Role**: `partner`
- **Access Level**: Tenant-specific admin access
- **Authentication**: JWT-based with role validation

### Feature-Based Access
Some features require additional subscription-based feature flags:
- **Voice Receptionist**: Requires `voice_intake` feature flag

## Dashboard Structure

### Main Navigation

| Section | Path | Description | Requirements |
|---------|------|-------------|--------------|
| Dashboard | `/admin` | Overview and quick stats | Partner role |
| Tenants | `/admin/tenants` | Tenant management | Partner role |
| Users | `/admin/users` | User administration | Partner role |
| Subscriptions | `/admin/subscriptions` | Subscription management | Partner role |
| Usage | `/admin/usage` | Usage monitoring and analytics | Partner role |
| Voice Receptionist | `/admin/voice` | Voice system management | Partner + `voice_intake` |
| Security | `/admin/security` | Security monitoring | Partner role |
| Logs | `/admin/logs` | System logs | Partner role |
| Analytics | `/admin/analytics` | Business analytics | Partner role |
| Settings | `/admin/settings` | System configuration | Partner role |

## Voice Receptionist Section

### Overview
The Voice Receptionist section provides comprehensive management of the voice intake system for law firms with the `voice_intake` feature enabled.

### Voice Menu Structure

| Page | Path | Description |
|------|------|-------------|
| Overview | `/admin/voice` | Voice system dashboard and quick actions |
| Call Logs | `/admin/voice/calls` | View and manage call history |
| Phone Numbers | `/admin/voice/numbers` | Purchase and configure Telnyx numbers |
| Failed Notifications | `/admin/voice/notifications` | Monitor system alerts and failures |

### Voice Features

#### Call Logs (`/admin/voice/calls`)
- **Purpose**: View and manage voice receptionist call history
- **Features**:
  - Real-time call log display
  - Call status tracking (completed, failed, busy, no-answer, in-progress)
  - Call duration and timing information
  - Recording and transcript access
  - Direction indicators (inbound/outbound)

#### Phone Numbers (`/admin/voice/numbers`)
- **Purpose**: Search, purchase, and configure Telnyx phone numbers
- **Features**:
  - Number search by area code and country
  - Real-time availability checking
  - One-click number purchasing
  - Forwarding configuration
  - Monthly cost tracking
  - Number type management (local, toll-free, mobile)

#### Failed Notifications (`/admin/voice/notifications`)
- **Purpose**: Monitor and manage failed voice system notifications
- **Features**:
  - Failed notification dashboard with statistics
  - Retry mechanism for failed notifications
  - Error message tracking
  - Notification type categorization (webhook, email, sms, call_failed, system_error)
  - Manual resolution marking

### Access Guards

The voice section implements strict access controls:

```typescript
// Guard implementation
const { user } = useAuth();
const { hasFeature } = useFeatures();

if (user.role !== 'partner' || !hasFeature('voice_intake')) {
  redirect('/');
}
```

### API Integration

Voice features integrate with dedicated API endpoints:

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/voice/calls` | GET | Fetch call logs |
| `/api/voice/numbers` | GET | Get purchased numbers |
| `/api/voice/numbers/search` | POST | Search available numbers |
| `/api/voice/numbers/purchase` | POST | Purchase a number |
| `/api/voice/notifications` | GET | Get failed notifications |
| `/api/voice/notifications/{id}/retry` | POST | Retry failed notification |
| `/api/voice/notifications/{id}/resolve` | POST | Mark notification as resolved |

## Components

### Reusable Components

#### MultiTenantTable
- **Location**: `frontend/src/components/ui/MultiTenantTable.tsx`
- **Purpose**: Standardized table component with optional tenant column
- **Features**:
  - Configurable columns with custom renderers
  - Loading states
  - Empty state handling
  - Tenant ID display (optional)

#### Voice Components
- **CallLogsTable**: `frontend/src/components/voice/CallLogsTable.tsx`
- **TelnyxNumbersTable**: `frontend/src/components/voice/TelnyxNumbersTable.tsx`
- **FailedNotificationsPanel**: `frontend/src/components/voice/FailedNotificationsPanel.tsx`

### Authentication Hooks

#### useFeatures
- **Location**: `frontend/src/hooks/useFeatures.ts`
- **Purpose**: Check tenant feature access based on subscription
- **Usage**:
```typescript
const { hasFeature, isLoading } = useFeatures();
const canAccessVoice = hasFeature('voice_intake');
```

## Testing

### Unit Tests
- **Voice Guard Tests**: `frontend/src/components/voice/__tests__/VoiceGuard.test.tsx`
- **Admin Sidebar Tests**: `frontend/src/components/admin/__tests__/AdminSidebar.test.tsx`

### E2E Tests
- **Voice Admin Flow**: `frontend/cypress/e2e/voice-admin.cy.ts`
- **Number Purchase Flow**: Simulates complete number search and purchase
- **Access Control**: Verifies proper role and feature-based access

### Test Coverage
- ✅ RBAC guard functionality
- ✅ Feature flag integration
- ✅ Voice menu visibility
- ✅ Number purchase workflow
- ✅ Access denial for unauthorized users

## Security Considerations

### Role-Based Access Control
- All admin routes require `partner` role
- Voice features require additional `voice_intake` feature flag
- JWT token validation on all API endpoints
- Tenant isolation enforced at database level

### Feature Flag Security
- Feature flags fetched from backend subscription API
- Tenant-specific feature validation
- No client-side feature flag manipulation
- Subscription-based access control

## Future Enhancements

### Planned Features
- Real-time call monitoring
- Advanced analytics and reporting
- Call recording management
- Webhook configuration UI
- Number porting capabilities
- Multi-language support

### Integration Points
- Telnyx API for number management
- Voice recording storage
- Webhook notification system
- Analytics and reporting engine

## Troubleshooting

### Common Issues

1. **Voice menu not visible**
   - Check user role (must be `partner`)
   - Verify `voice_intake` feature flag
   - Check subscription status

2. **API errors**
   - Verify JWT token validity
   - Check tenant ID in requests
   - Confirm backend API availability

3. **Number purchase failures**
   - Check Telnyx API credentials
   - Verify subscription limits
   - Check payment method status

### Debug Information
- Use browser dev tools to inspect network requests
- Check console for authentication errors
- Verify feature flags in localStorage (development only)
