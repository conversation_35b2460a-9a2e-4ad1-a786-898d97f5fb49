describe('Voice Queue RBAC', () => {
  beforeEach(() => {
    // Mock the authentication and feature flags
    cy.window().then((win) => {
      win.localStorage.setItem('auth-token', 'mock-token');
    });
  });

  describe('Tenant Queue Widget', () => {
    it('should show queue widget for partner with voice_intake feature', () => {
      // Mock partner user with voice_intake feature
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 200,
        body: {
          id: 'partner-user',
          email: '<EMAIL>',
          role: 'partner',
          tenant_id: 'test-tenant',
        },
      });

      cy.intercept('GET', '/api/features', {
        statusCode: 200,
        body: {
          features: ['voice_intake'],
        },
      });

      cy.intercept('GET', '/api/v1/queue/', {
        statusCode: 200,
        body: {
          tenant_id: 'test-tenant',
          queued: 5,
          processing: 2,
          completed: 100,
          failed: 3,
          total_today: 103,
          avg_processing_time_seconds: 45.5,
          last_updated: '2024-01-15T10:30:00Z',
          worker_status: 'healthy',
        },
      });

      cy.visit('/admin/voice');
      
      // Should see the queue widget
      cy.contains('Call Queue Status').should('be.visible');
      cy.contains('5').should('be.visible'); // queued count
      cy.contains('2').should('be.visible'); // processing count
      cy.contains('Clear Queue (5)').should('be.visible');
    });

    it('should not show voice page for staff without voice_intake feature', () => {
      // Mock staff user without voice_intake feature
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 200,
        body: {
          id: 'staff-user',
          email: '<EMAIL>',
          role: 'staff',
          tenant_id: 'test-tenant',
        },
      });

      cy.intercept('GET', '/api/features', {
        statusCode: 200,
        body: {
          features: [], // No voice_intake feature
        },
      });

      cy.visit('/admin/voice');
      
      // Should be redirected away from voice page
      cy.url().should('not.include', '/admin/voice');
    });

    it('should not show voice page for non-partner role', () => {
      // Mock attorney user (not partner)
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 200,
        body: {
          id: 'attorney-user',
          email: '<EMAIL>',
          role: 'attorney',
          tenant_id: 'test-tenant',
        },
      });

      cy.intercept('GET', '/api/features', {
        statusCode: 200,
        body: {
          features: ['voice_intake'],
        },
      });

      cy.visit('/admin/voice');
      
      // Should be redirected away from voice page
      cy.url().should('not.include', '/admin/voice');
    });

    it('should allow clearing queue when items are queued', () => {
      // Mock partner user with voice_intake feature
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 200,
        body: {
          id: 'partner-user',
          email: '<EMAIL>',
          role: 'partner',
          tenant_id: 'test-tenant',
        },
      });

      cy.intercept('GET', '/api/features', {
        statusCode: 200,
        body: {
          features: ['voice_intake'],
        },
      });

      cy.intercept('GET', '/api/v1/queue/', {
        statusCode: 200,
        body: {
          tenant_id: 'test-tenant',
          queued: 5,
          processing: 2,
          completed: 100,
          failed: 3,
          total_today: 103,
          avg_processing_time_seconds: 45.5,
          last_updated: '2024-01-15T10:30:00Z',
          worker_status: 'healthy',
        },
      });

      cy.intercept('POST', '/api/v1/queue/clear', {
        statusCode: 200,
        body: { success: true },
      });

      cy.visit('/admin/voice');
      
      // Clear queue button should be enabled
      cy.contains('Clear Queue (5)').should('not.be.disabled');
      cy.contains('Clear Queue (5)').click();
      
      // Should make the API call
      cy.wait('@POST /api/v1/queue/clear');
    });

    it('should disable clear queue button when queue is empty', () => {
      // Mock partner user with voice_intake feature
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 200,
        body: {
          id: 'partner-user',
          email: '<EMAIL>',
          role: 'partner',
          tenant_id: 'test-tenant',
        },
      });

      cy.intercept('GET', '/api/features', {
        statusCode: 200,
        body: {
          features: ['voice_intake'],
        },
      });

      cy.intercept('GET', '/api/v1/queue/', {
        statusCode: 200,
        body: {
          tenant_id: 'test-tenant',
          queued: 0, // Empty queue
          processing: 0,
          completed: 100,
          failed: 3,
          total_today: 103,
          avg_processing_time_seconds: 45.5,
          last_updated: '2024-01-15T10:30:00Z',
          worker_status: 'healthy',
        },
      });

      cy.visit('/admin/voice');
      
      // Clear queue button should be disabled
      cy.contains('Clear Queue (0)').should('be.disabled');
    });
  });

  describe('Super Admin Queue Monitor', () => {
    it('should show system queue monitor for super admin', () => {
      // Mock super admin user
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 200,
        body: {
          id: 'superadmin-user',
          email: '<EMAIL>',
          role: 'partner',
          tenant_id: 'admin-tenant',
          is_super_admin: true,
        },
      });

      cy.intercept('GET', '/api/v1/queue/all', {
        statusCode: 200,
        body: {
          tenants: [
            {
              tenant_id: 'tenant-1',
              queued: 5,
              processing: 2,
              completed: 100,
              failed: 3,
              total_today: 103,
              avg_processing_time_seconds: 45.5,
              last_updated: '2024-01-15T10:30:00Z',
              worker_status: 'healthy',
            },
            {
              tenant_id: 'tenant-2',
              queued: 2,
              processing: 1,
              completed: 50,
              failed: 1,
              total_today: 51,
              avg_processing_time_seconds: 30.2,
              last_updated: '2024-01-15T10:25:00Z',
              worker_status: 'healthy',
            },
          ],
          totals: {
            queued: 7,
            processing: 3,
            completed: 150,
            failed: 4,
            total_today: 154,
            active_tenants: 2,
          },
          worker_health: {
            status: 'healthy',
            last_heartbeat: '2024-01-15T10:30:00Z',
            workers_active: 2,
            workers_total: 2,
          },
          last_updated: '2024-01-15T10:30:00Z',
        },
      });

      cy.visit('/superadmin/voice/queues');
      
      // Should see the system monitor
      cy.contains('System Queue Monitor').should('be.visible');
      cy.contains('Total Queued').should('be.visible');
      cy.contains('7').should('be.visible'); // total queued
      cy.contains('3').should('be.visible'); // total processing
      
      // Should see tenant table
      cy.contains('tenant-1').should('be.visible');
      cy.contains('tenant-2').should('be.visible');
    });

    it('should not allow regular users to access super admin queue monitor', () => {
      // Mock regular partner user (not super admin)
      cy.intercept('GET', '/api/auth/profile', {
        statusCode: 200,
        body: {
          id: 'partner-user',
          email: '<EMAIL>',
          role: 'partner',
          tenant_id: 'test-tenant',
          is_super_admin: false,
        },
      });

      cy.visit('/superadmin/voice/queues');
      
      // Should be redirected away from super admin area
      cy.url().should('not.include', '/superadmin');
    });
  });
});
